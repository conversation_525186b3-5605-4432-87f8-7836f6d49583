FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy the application into the container.
COPY . /app

# Install the application dependencies.
WORKDIR /app
# RUN uv add uvicorn
RUN uv sync --frozen --no-cache

# Copy wait script
COPY wait-for-rabbitmq.py /app/

# Run the application with RabbitMQ wait
CMD ["/bin/bash", "-c", "/app/.venv/bin/python wait-for-rabbitmq.py && /app/.venv/bin/fastapi run main.py --port 8300 --host 0.0.0.0"]

#helo
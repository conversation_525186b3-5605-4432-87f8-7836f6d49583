#!/usr/bin/env python3
"""
Wait for RabbitMQ to be ready before starting the application.
"""

import asyncio
import sys
import time
import aio_pika
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def wait_for_rabbitmq(max_attempts=30, delay=2):
    """Wait for RabbitMQ to be ready"""
    
    # Get RabbitMQ URL from environment
    import os
    rabbitmq_url = os.getenv("RABBITMQ_URL", "amqp://admin:secret@rabbitmq:5672/")
    
    logger.info(f"Waiting for RabbitMQ at {rabbitmq_url}")
    
    for attempt in range(max_attempts):
        try:
            # Try to connect to RabbitMQ
            connection = await aio_pika.connect_robust(rabbitmq_url)
            await connection.close()
            
            logger.info("✅ RabbitMQ is ready!")
            return True
            
        except Exception as e:
            logger.info(f"⏳ Attempt {attempt + 1}/{max_attempts}: RabbitMQ not ready yet ({str(e)})")
            if attempt < max_attempts - 1:
                await asyncio.sleep(delay)
            else:
                logger.error("❌ RabbitMQ failed to become ready")
                return False
    
    return False


async def main():
    """Main function"""
    success = await wait_for_rabbitmq()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())

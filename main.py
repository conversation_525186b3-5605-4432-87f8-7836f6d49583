from fastapi import FastAP<PERSON>, responses
from fastapi.middleware.cors import CORSMiddleware
from app.v1.api import router as v1_api
from app.v1.queue_management.config import initialize_queue_system, shutdown_queue_system
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)

app = FastAPI(
    title="Aroma Backend API",
    description="Backend API with RabbitMQ job queue system",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.mount("/v1", v1_api)


@app.on_event("startup")
async def startup_event():
    """Initialize the queue system on application startup"""
    try:
        logger.info("Initializing RabbitMQ queue system...")
        await initialize_queue_system()
        logger.info("Queue system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize queue system: {str(e)}")
        # Don't fail startup, but log the error
        # The queue endpoints will handle connection errors gracefully


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup the queue system on application shutdown"""
    try:
        logger.info("Shutting down queue system...")
        await shutdown_queue_system()
        logger.info("Queue system shutdown complete")
    except Exception as e:
        logger.error(f"Error during queue system shutdown: {str(e)}")


@app.get("/")
async def root():
    return responses.RedirectResponse(url="/v1/docs")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "aroma-backend-v2"}

# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)


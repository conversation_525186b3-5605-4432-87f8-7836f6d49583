pipeline {
    agent any

    stages {
        stage('Setup') {
            steps {
                withCredentials([file(credentialsId: 'aroma-v2-backend-env', variable: 'ENV_FILE')]) {
                    sh '''
                        cp "$ENV_FILE" .env

                        # Validate required environment variables
                        echo "🔍 Validating environment configuration..."
                        if ! grep -q "RABBITMQ_DEFAULT_USER" .env; then
                            echo "❌ RABBITMQ_DEFAULT_USER not found in .env"
                            exit 1
                        fi

                        if ! grep -q "MAX_CONCURRENT_JOBS" .env; then
                            echo "⚠️  MAX_CONCURRENT_JOBS not set, using default"
                        fi

                        echo "✅ Environment validation passed"
                    '''
                }
            }
        }

        stage('Build ') {
            steps {
                script {
                    try {
                        sh '''
                            docker compose build --no-cache
                        '''
                        echo "✅ Deployed successfully"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    
        stage('Deploy') {
            steps {
                script {
                    try {
                        sh '''
                            # Stop existing services
                            docker compose down

                            # Start services with proper ordering
                            docker compose up -d

                            # Scale workers based on environment (optional)
                            # Uncomment and adjust based on your production needs:
                            # docker compose up -d --scale workers=3

                            # Wait for services to be ready
                            echo "⏳ Waiting for services to start..."
                            sleep 30
                        '''
                        echo "✅ Services started successfully"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }

        stage('Health Check') {
            steps {
                script {
                    try {
                        sh '''
                            # Wait for app to be healthy
                            echo "🏥 Checking application health..."
                            for i in {1..12}; do
                                if curl -f http://localhost:8300/health > /dev/null 2>&1; then
                                    echo "✅ App is healthy"
                                    break
                                fi
                                echo "⏳ Waiting for app to be ready... (attempt $i/12)"
                                sleep 10
                            done

                            # Final health check
                            curl -f http://localhost:8300/health || exit 1

                            # Check queue system
                            echo "📊 Checking queue system..."
                            docker compose ps | grep -E "(app|workers|rabbitmq)" | grep -v "Exit"

                            # Show service status
                            echo "📋 Service status:"
                            docker compose ps
                        '''
                        echo "✅ Health checks passed"
                    } catch (Exception err) {
                        echo "❌ Health checks failed"
                        sh 'docker compose logs --tail=50'
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                // Show final service status
                sh '''
                    echo "📊 Final deployment status:"
                    docker compose ps || true
                '''
            }
        }
        success {
            echo "🎉 Deployment completed successfully!"
            script {
                sh '''
                    echo "✅ Queue system status:"
                    curl -s http://localhost:8300/v1/jobs/queue/status || echo "Queue status check failed"
                '''
            }
        }
        failure {
            echo "❌ Deployment failed!"
            script {
                sh '''
                    echo "📋 Service logs for debugging:"
                    docker compose logs --tail=100 || true

                    echo "🔍 Container status:"
                    docker compose ps || true
                '''
            }
        }
        cleanup {
            sh '''
                # Clean up old images and containers
                docker system prune -f

                # Remove dangling images
                docker image prune -f
            '''
        }
    }
}
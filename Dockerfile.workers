FROM python:3.12-slim

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Install system dependencies for network tools
RUN apt-get update && apt-get install -y \
    netcat-openbsd \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the application into the container
COPY . /app
WORKDIR /app

# Install dependencies in venv
RUN uv sync --frozen --no-cache

# Copy wait script
COPY wait-for-rabbitmq-workers.py /app/

# Activate venv by using its Python binary with wait
CMD ["/bin/bash", "-c", "/app/.venv/bin/python wait-for-rabbitmq-workers.py && /app/.venv/bin/python start_workers.py --workers 10"]

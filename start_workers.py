#!/usr/bin/env python3
"""
Job Queue Workers Startup Script

This script starts the RabbitMQ job queue workers that process jobs from the queue.
The workers run independently from the main FastAPI application.

Usage:
    python start_workers.py [--workers NUM_WORKERS]

Environment Variables:
    NUM_WORKERS: Number of worker processes to start (default: 5)
    RABBITMQ_URL: RabbitMQ connection URL
    RABBITMQ_DEFAULT_USER: RabbitMQ username
    RABBITMQ_DEFAULT_PASS: RabbitMQ password
    MAX_CONCURRENT_JOBS: Maximum concurrent job executions (default: 20)
    MAX_QUEUE_LENGTH: Maximum queue length (default: 50)

Example:
    # Start with default 5 workers
    python start_workers.py
    
    # Start with 10 workers
    python start_workers.py --workers 10
    
    # Or set via environment variable
    NUM_WORKERS=8 python start_workers.py
"""

import argparse
import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.v1.queue_management.worker_manager import start_workers
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


def main():
    parser = argparse.ArgumentParser(
        description="Start RabbitMQ job queue workers",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=int(os.getenv("NUM_WORKERS", "5")),
        help="Number of workers to start (default: 5)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Log level (default: INFO)"
    )
    
    args = parser.parse_args()
    
    # Set log level
    import logging
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    logger.info(f"Starting {args.workers} job queue workers...")
    logger.info(f"Log level: {args.log_level}")
    
    # Check required environment variables
    required_env_vars = ["MONGO_URI"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        sys.exit(1)
    
    # Optional environment variables with defaults
    rabbitmq_user = os.getenv("RABBITMQ_DEFAULT_USER", "guest")
    rabbitmq_pass = os.getenv("RABBITMQ_DEFAULT_PASS", "guest")
    rabbitmq_host = os.getenv("RABBITMQ_HOST", "localhost")
    
    logger.info(f"RabbitMQ connection: {rabbitmq_user}@{rabbitmq_host}")
    logger.info(f"Max concurrent jobs: {os.getenv('MAX_CONCURRENT_JOBS', '20')}")
    logger.info(f"Max queue length: {os.getenv('MAX_QUEUE_LENGTH', '50')}")
    
    try:
        asyncio.run(start_workers(args.workers))
    except KeyboardInterrupt:
        logger.info("Workers stopped by user")
    except Exception as e:
        logger.error(f"Workers failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

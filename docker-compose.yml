version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aroma-app
    network_mode: host
    env_file:
      - .env
    environment:
      - RABBITMQ_HOST=localhost
      - RABBITMQ_URL=amqp://admin:secret@localhost:5672/
    restart: unless-stopped
    depends_on:
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8300/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

  rabbitmq:
    image: rabbitmq:3-management
    container_name: aroma-rabbitmq
    hostname: rabbitmq
    network_mode: host
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
    restart: unless-stopped
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

  workers:
    build:
      context: .
      dockerfile: Dockerfile.workers
    container_name: aroma-workers
    network_mode: host
    env_file:
      - .env
    environment:
      - RABBITMQ_HOST=localhost
      - RABBITMQ_URL=amqp://admin:secret@localhost:5672/
      - NUM_WORKERS=10
      - MAX_CONCURRENT_JOBS=15
      - MAX_QUEUE_LENGTH=75
    restart: unless-stopped
    depends_on:
      rabbitmq:
        condition: service_healthy
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
          cpus: '0.8'
        reservations:
          memory: 256M
          cpus: '0.2'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

volumes:
  rabbitmq_data:
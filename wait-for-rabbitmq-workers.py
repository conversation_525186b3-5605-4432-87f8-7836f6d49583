#!/usr/bin/env python3
"""
Simple wait script for workers to ensure RabbitMQ is ready.
This script waits for RabbitMQ to be accessible before starting workers.
"""

import time
import socket
import sys
import os

def wait_for_rabbitmq(host="rabbitmq", port=5672, max_attempts=30, delay=5):
    """Wait for RabbitMQ to be accessible"""
    
    print(f"🔍 Waiting for RabbitMQ at {host}:{port}")
    
    for attempt in range(max_attempts):
        try:
            # Try to connect to RabbitMQ port
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ RabbitMQ is ready at {host}:{port}")
                return True
            else:
                print(f"⏳ Attempt {attempt + 1}/{max_attempts}: RabbitMQ not ready yet")
                
        except Exception as e:
            print(f"⏳ Attempt {attempt + 1}/{max_attempts}: Connection failed - {e}")
        
        if attempt < max_attempts - 1:
            time.sleep(delay)
    
    print(f"❌ RabbitMQ failed to become ready after {max_attempts} attempts")
    return False

def main():
    """Main function"""
    
    # Get RabbitMQ connection details from environment
    rabbitmq_host = os.getenv("RABBITMQ_HOST", "rabbitmq")
    rabbitmq_port = int(os.getenv("RABBITMQ_PORT", "5672"))
    
    print(f"🚀 Worker startup - waiting for RabbitMQ")
    print(f"📍 Target: {rabbitmq_host}:{rabbitmq_port}")
    
    # Wait for RabbitMQ
    if wait_for_rabbitmq(rabbitmq_host, rabbitmq_port):
        print("🎉 RabbitMQ is ready! Starting workers...")
        sys.exit(0)
    else:
        print("💥 Failed to connect to RabbitMQ. Exiting.")
        sys.exit(1)

if __name__ == "__main__":
    main()

from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, Literal, List, Optional
from pymongo.database import Database
from pymongo.asynchronous.database import AsyncDatabase
from minio import Minio

from .role import Role

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Role


    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)


    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        )
    # class Config:
    #     arbitrary_types_allowed = True

class UserTenantDB(BaseModel):
    tenant_id: str
    db: Database
    user: User
    async_db: AsyncDatabase
    minio_bucket_name: Optional[str] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    @property
    def minio_client(self) -> Minio:
        """
        Initialize MinIO Client based on tenant configuration.
        """
        env_var = self.db.config.find_one({"name": "minio-config"})
        minio_config = env_var.get("value")
        minio_client = Minio(
                endpoint=minio_config["minio_url"],
                access_key=minio_config["access_key"],
                secret_key=minio_config["secret_key"],
                secure=minio_config.get("secure", False)  # Use secure setting from config, default to False
        )
        self.minio_bucket_name = minio_config["bucket_name"]
        return minio_client

class AgentInvitation(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"]
    # permissions: Dict[str, bool]| None={"all":True}
    
class AgentRegistration(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"]
    password: str = Field(
        ...,
        json_schema_extra={"example": "strongpassword123"}
    )
    token: str = Field(
        ...,
        json_schema_extra={"example": "invitation_token_here"}
    )

import asyncio
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timezone
import aio_pika
from aio_pika import Message, DeliveryMode, ExchangeType
from aio_pika.abc import AbstractConnection, AbstractChannel, AbstractQueue, AbstractExchange

from .models import JobQueueMessage, QueueStatus, QueueConfig, QueueMetrics
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


class JobQueueManager:
    """
    RabbitMQ-based job queue manager with concurrency control and queue limits.
    
    Features:
    - Maximum 20 concurrent job executions
    - Maximum 50 jobs in queue
    - Automatic retry mechanism
    - Connection management and error handling
    - Queue monitoring and metrics
    """
    
    def __init__(self, config: QueueConfig):
        self.config = config
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[AbstractChannel] = None
        self.queue: Optional[AbstractQueue] = None
        self.exchange: Optional[AbstractExchange] = None
        self.active_jobs: Dict[str, datetime] = {}
        self.metrics = QueueMetrics()
        self.startup_time = datetime.now(timezone.utc)
        self._connection_lock = asyncio.Lock()
        self._queued_jobs_count = 0  # Internal counter for queued jobs
        
    async def connect(self) -> None:
        """Establish connection to RabbitMQ and set up queue infrastructure"""
        async with self._connection_lock:
            if self.connection and not self.connection.is_closed:
                return
                
            try:
                logger.info(f"Connecting to RabbitMQ at {self.config.rabbitmq_url}")
                self.connection = await aio_pika.connect_robust(
                    self.config.rabbitmq_url,
                    heartbeat=600,
                    blocked_connection_timeout=300,
                )
                
                self.channel = await self.connection.channel()
                await self.channel.set_qos(prefetch_count=self.config.prefetch_count)
                
                # Declare exchange
                self.exchange = await self.channel.declare_exchange(
                    self.config.exchange_name,
                    ExchangeType.DIRECT,
                    durable=True
                )
                
                # Declare queue with TTL and max length
                queue_arguments = {
                    "x-message-ttl": self.config.message_ttl,
                    "x-max-length": self.config.max_queue_length,
                    "x-overflow": "reject-publish"  # Reject new messages when queue is full
                }
                
                self.queue = await self.channel.declare_queue(
                    self.config.queue_name,
                    durable=True,
                    arguments=queue_arguments
                )
                
                # Bind queue to exchange
                await self.queue.bind(self.exchange, self.config.routing_key)
                
                logger.info("Successfully connected to RabbitMQ and set up queue infrastructure")
                
            except Exception as e:
                logger.error(f"Failed to connect to RabbitMQ: {str(e)}")
                raise
    
    async def disconnect(self) -> None:
        """Close RabbitMQ connection"""
        try:
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
                logger.info("Disconnected from RabbitMQ")
        except Exception as e:
            logger.error(f"Error disconnecting from RabbitMQ: {str(e)}")
    
    async def enqueue_job(self, job_message: JobQueueMessage) -> bool:
        """
        Add a job to the queue if capacity allows.
        
        Returns:
            bool: True if job was queued successfully, False if queue is full
        """
        await self.connect()
        
        try:
            # Check if we're at capacity
            if self._queued_jobs_count >= self.config.max_queue_length:
                logger.warning(f"Queue is full ({self._queued_jobs_count}/{self.config.max_queue_length}), cannot enqueue job {job_message.job_id}")
                return False
            
            # Serialize job message
            message_body = job_message.model_dump_json()
            
            # Create RabbitMQ message
            message = Message(
                message_body.encode(),
                delivery_mode=DeliveryMode.PERSISTENT,
                priority=job_message.priority,
                message_id=job_message.job_id,
                timestamp=datetime.now(timezone.utc),
                headers={
                    "job_id": job_message.job_id,
                    "tenant_id": job_message.tenant_id,
                    "process_name": job_message.process_name,
                    "retry_count": job_message.retry_count
                }
            )
            
            # Publish message
            await self.exchange.publish(
                message,
                routing_key=self.config.routing_key
            )

            # Increment queued jobs counter
            self._queued_jobs_count += 1

            logger.info(f"Successfully queued job {job_message.job_id} (queue size: {self._queued_jobs_count})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to enqueue job {job_message.job_id}: {str(e)}")
            return False
    
    async def get_queue_status(self) -> QueueStatus:
        """Get current queue status and metrics"""
        await self.connect()

        try:
            # Get actual queue info from RabbitMQ Management API for accurate data
            active_workers = 0
            actual_queued = self._queued_jobs_count

            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    # RabbitMQ Management API to get real queue info
                    # Use the correct RabbitMQ credentials (admin:secret)
                    url = "***********************************/api/queues/%2F/job_queue"
                    async with session.get(url) as response:
                        if response.status == 200:
                            queue_info = await response.json()
                            actual_queued = queue_info.get('messages', self._queued_jobs_count)
                            consumers = queue_info.get('consumers', 0)

                            # Use consumer count as active workers (more accurate than internal tracking)
                            # Each worker creates one consumer, so consumer count = active workers
                            active_workers = min(consumers, self.config.max_concurrent_jobs)

                            logger.debug(f"RabbitMQ info: {actual_queued} messages, {consumers} consumers")
                        else:
                            # Fallback to internal tracking
                            active_workers = len(self.active_jobs)
                            logger.debug(f"Management API unavailable, using internal tracking: {active_workers}")
            except Exception as e:
                logger.debug(f"Could not get RabbitMQ management info: {e}")
                # Fallback to internal tracking
                active_workers = len(self.active_jobs)

            return QueueStatus(
                total_queued=actual_queued,
                active_workers=active_workers,
                max_concurrent_jobs=self.config.max_concurrent_jobs,
                max_queue_length=self.config.max_queue_length,
                queue_full=actual_queued >= self.config.max_queue_length,
                workers_at_capacity=active_workers >= self.config.max_concurrent_jobs
            )
            
        except Exception as e:
            logger.error(f"Failed to get queue status: {str(e)}")
            # Return default status on error
            return QueueStatus(
                total_queued=0,
                active_workers=len(self.active_jobs),
                max_concurrent_jobs=self.config.max_concurrent_jobs,
                max_queue_length=self.config.max_queue_length,
                queue_full=False,
                workers_at_capacity=len(self.active_jobs) >= self.config.max_concurrent_jobs
            )
    
    def mark_job_started(self, job_id: str) -> None:
        """Mark a job as started (for concurrency tracking)"""
        self.active_jobs[job_id] = datetime.now(timezone.utc)
        logger.debug(f"Marked job {job_id} as started. Active jobs: {len(self.active_jobs)}")
    
    def mark_job_completed(self, job_id: str, success: bool = True) -> None:
        """Mark a job as completed (for concurrency tracking and metrics)"""
        if job_id in self.active_jobs:
            start_time = self.active_jobs.pop(job_id)
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            # Decrement queued jobs counter when job is completed
            if self._queued_jobs_count > 0:
                self._queued_jobs_count -= 1

            # Update metrics
            if success:
                self.metrics.total_jobs_processed += 1
            else:
                self.metrics.total_jobs_failed += 1

            # Update average processing time
            total_jobs = self.metrics.total_jobs_processed + self.metrics.total_jobs_failed
            if total_jobs > 0:
                current_avg = self.metrics.average_processing_time
                self.metrics.average_processing_time = (
                    (current_avg * (total_jobs - 1) + processing_time) / total_jobs
                )

            logger.debug(f"Marked job {job_id} as completed. Active jobs: {len(self.active_jobs)}")
    
    async def get_metrics(self) -> QueueMetrics:
        """Get queue metrics and performance statistics"""
        uptime = (datetime.now(timezone.utc) - self.startup_time).total_seconds()
        
        # Calculate throughput (jobs per minute)
        if uptime > 0:
            throughput = (self.metrics.total_jobs_processed * 60) / uptime
        else:
            throughput = 0.0
        
        self.metrics.uptime = uptime
        self.metrics.queue_throughput = throughput
        
        return self.metrics
    
    async def purge_queue(self) -> int:
        """Purge all messages from the queue. Returns number of purged messages."""
        await self.connect()
        
        try:
            result = await self.queue.purge()
            logger.info(f"Purged {result} messages from queue")
            return result
        except Exception as e:
            logger.error(f"Failed to purge queue: {str(e)}")
            return 0

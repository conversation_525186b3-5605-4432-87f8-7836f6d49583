import os
from typing import Optional, TYPE_CHECKING
from .models import QueueConfig
from app.core.helper.logger import setup_new_logging

if TYPE_CHECKING:
    from .queue_manager import JobQueueManager

logger = setup_new_logging(__name__)


def get_queue_config() -> QueueConfig:
    """
    Get queue configuration from environment variables with sensible defaults.
    
    Environment variables:
    - RABBITMQ_URL: RabbitMQ connection URL
    - RABBITMQ_DEFAULT_USER: RabbitMQ username  
    - RABBITMQ_DEFAULT_PASS: RabbitMQ password
    - MAX_CONCURRENT_JOBS: Maximum concurrent job executions (default: 20)
    - MAX_QUEUE_LENGTH: Maximum queue length (default: 50)
    """
    
    # Build RabbitMQ URL from environment variables
    rabbitmq_user = os.getenv("RABBITMQ_DEFAULT_USER", "guest")
    rabbitmq_pass = os.getenv("RABBITMQ_DEFAULT_PASS", "guest")
    rabbitmq_host = os.getenv("RABBITMQ_HOST", "localhost")
    rabbitmq_port = os.getenv("RABBITMQ_PORT", "5672")
    
    # Check if full URL is provided, otherwise construct it
    rabbitmq_url = os.getenv("RABBITMQ_URL")
    if not rabbitmq_url:
        rabbitmq_url = f"amqp://{rabbitmq_user}:{rabbitmq_pass}@{rabbitmq_host}:{rabbitmq_port}/"
    
    config = QueueConfig(
        max_concurrent_jobs=int(os.getenv("MAX_CONCURRENT_JOBS", "20")),
        max_queue_length=int(os.getenv("MAX_QUEUE_LENGTH", "50")),
        rabbitmq_url=rabbitmq_url,
        queue_name=os.getenv("JOB_QUEUE_NAME", "job_queue"),
        exchange_name=os.getenv("JOB_EXCHANGE_NAME", "job_exchange"),
        routing_key=os.getenv("JOB_ROUTING_KEY", "job.process"),
        prefetch_count=int(os.getenv("WORKER_PREFETCH_COUNT", "1")),
        message_ttl=int(os.getenv("MESSAGE_TTL", "3600000")),  # 1 hour in milliseconds
        retry_delay=int(os.getenv("RETRY_DELAY", "60"))  # 60 seconds
    )
    
    logger.info(f"Queue configuration loaded: max_concurrent={config.max_concurrent_jobs}, "
                f"max_queue_length={config.max_queue_length}, "
                f"rabbitmq_host={rabbitmq_host}")
    
    return config


# Global queue manager instance
_queue_manager: Optional['JobQueueManager'] = None


def get_queue_manager() -> 'JobQueueManager':
    """Get or create the global queue manager instance"""
    global _queue_manager
    
    if _queue_manager is None:
        from .queue_manager import JobQueueManager
        config = get_queue_config()
        _queue_manager = JobQueueManager(config)
        logger.info("Created new queue manager instance")
    
    return _queue_manager


async def initialize_queue_system() -> None:
    """Initialize the queue system on application startup"""
    import asyncio

    max_retries = 5
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        try:
            queue_manager = get_queue_manager()
            await queue_manager.connect()
            logger.info("Queue system initialized successfully")
            return
        except Exception as e:
            logger.warning(f"Failed to initialize queue system (attempt {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logger.error("Failed to initialize queue system after all retries")
                # Don't raise - let the app start without queue system
                # The queue endpoints will handle connection errors gracefully


async def shutdown_queue_system() -> None:
    """Shutdown the queue system on application shutdown"""
    global _queue_manager
    
    if _queue_manager:
        try:
            await _queue_manager.disconnect()
            logger.info("Queue system shutdown successfully")
        except Exception as e:
            logger.error(f"Error during queue system shutdown: {str(e)}")
        finally:
            _queue_manager = None

import asyncio
import signal
import sys
from typing import List, Dict
from datetime import datetime, timezone

from .config import get_queue_manager
from .worker import JobWorker
from .models import WorkerStatus
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


class WorkerManager:
    """
    Manages multiple job workers for processing the RabbitMQ queue.
    
    Features:
    - Starts and manages multiple worker processes
    - Handles graceful shutdown
    - Monitors worker health
    - Automatic worker restart on failure
    """
    
    def __init__(self, num_workers: int = 5):
        self.num_workers = num_workers
        self.workers: List[JobWorker] = []
        self.worker_tasks: List[asyncio.Task] = []
        self.queue_manager = get_queue_manager()
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        
    async def start(self) -> None:
        """Start all workers"""
        if self.is_running:
            logger.warning("Worker manager is already running")
            return
            
        self.is_running = True
        logger.info(f"Starting worker manager with {self.num_workers} workers")
        
        try:
            # Initialize queue system
            await self.queue_manager.connect()
            
            # Start workers
            for i in range(self.num_workers):
                worker = JobWorker(self.queue_manager, worker_id=f"worker-{i+1}")
                self.workers.append(worker)
                
                # Start worker in background task
                task = asyncio.create_task(
                    self._run_worker_with_restart(worker),
                    name=f"worker-{i+1}-task"
                )
                self.worker_tasks.append(task)
                
            logger.info(f"Started {len(self.workers)} workers successfully")
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Error in worker manager: {str(e)}")
            raise
        finally:
            await self.stop()
    
    async def stop(self) -> None:
        """Stop all workers gracefully"""
        if not self.is_running:
            return
            
        logger.info("Stopping worker manager...")
        self.is_running = False
        
        # Stop all workers
        for worker in self.workers:
            try:
                await worker.stop()
            except Exception as e:
                logger.error(f"Error stopping worker {worker.worker_id}: {str(e)}")
        
        # Cancel all worker tasks
        for task in self.worker_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.error(f"Error cancelling worker task: {str(e)}")
        
        # Disconnect from queue
        try:
            await self.queue_manager.disconnect()
        except Exception as e:
            logger.error(f"Error disconnecting from queue: {str(e)}")
        
        logger.info("Worker manager stopped")
    
    async def _run_worker_with_restart(self, worker: JobWorker) -> None:
        """Run a worker with automatic restart on failure"""
        while self.is_running:
            try:
                logger.info(f"Starting worker {worker.worker_id}")
                await worker.start()
            except Exception as e:
                logger.error(f"Worker {worker.worker_id} failed: {str(e)}")
                
                if self.is_running:
                    logger.info(f"Restarting worker {worker.worker_id} in 5 seconds...")
                    await asyncio.sleep(5)
                else:
                    break
    
    def get_worker_statuses(self) -> List[WorkerStatus]:
        """Get status of all workers"""
        return [worker.get_status() for worker in self.workers]
    
    def signal_shutdown(self) -> None:
        """Signal the worker manager to shutdown"""
        self._shutdown_event.set()


# Global worker manager instance
_worker_manager: WorkerManager = None


def get_worker_manager(num_workers: int = 5) -> WorkerManager:
    """Get or create the global worker manager instance"""
    global _worker_manager
    
    if _worker_manager is None:
        _worker_manager = WorkerManager(num_workers)
        logger.info(f"Created worker manager with {num_workers} workers")
    
    return _worker_manager


async def start_workers(num_workers: int = 5) -> None:
    """Start the worker manager"""
    manager = get_worker_manager(num_workers)
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        manager.signal_shutdown()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await manager.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Worker manager error: {str(e)}")
        raise


if __name__ == "__main__":
    """
    Run the worker manager as a standalone script.
    
    Usage:
        python -m app.v1.queue_management.worker_manager
    
    Environment variables:
        NUM_WORKERS: Number of workers to start (default: 5)
    """
    import os
    
    num_workers = int(os.getenv("NUM_WORKERS", "5"))
    
    logger.info(f"Starting job queue workers (count: {num_workers})")
    
    try:
        asyncio.run(start_workers(num_workers))
    except KeyboardInterrupt:
        logger.info("Worker manager stopped by user")
    except Exception as e:
        logger.error(f"Worker manager failed: {str(e)}")
        sys.exit(1)

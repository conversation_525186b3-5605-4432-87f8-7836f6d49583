# RabbitMQ Job Queue System

This module implements a scalable job queue system using RabbitMQ for the Aroma Backend API. It provides concurrency control, queue management, and reliable job processing.

## Features

- **Concurrency Control**: Maximum 20 concurrent job executions
- **Queue Limits**: Maximum 50 jobs in queue with overflow protection
- **Reliable Processing**: Message acknowledgment and retry mechanism
- **Monitoring**: Queue status, metrics, and worker health monitoring
- **Graceful Shutdown**: Proper cleanup and connection management
- **Error Handling**: Comprehensive error handling and logging

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │───▶│   RabbitMQ      │───▶│   Job Workers   │
│                 │    │   Queue         │    │                 │
│ execute_job()   │    │                 │    │ process_job()   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Components

1. **JobQueueManager**: Manages RabbitMQ connection and queue operations
2. **JobWorker**: Processes jobs from the queue using existing job processors
3. **WorkerManager**: Manages multiple workers with health monitoring
4. **Queue Models**: Pydantic models for type safety and serialization

## Configuration

### Environment Variables

```bash
# RabbitMQ Configuration
RABBITMQ_URL=amqp://user:pass@localhost:5672/
RABBITMQ_DEFAULT_USER=guest
RABBITMQ_DEFAULT_PASS=guest
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672

# Queue Configuration
MAX_CONCURRENT_JOBS=20          # Maximum concurrent job executions
MAX_QUEUE_LENGTH=50             # Maximum jobs in queue
NUM_WORKERS=5                   # Number of worker processes

# Queue Names (optional)
JOB_QUEUE_NAME=job_queue
JOB_EXCHANGE_NAME=job_exchange
JOB_ROUTING_KEY=job.process

# Performance Tuning (optional)
WORKER_PREFETCH_COUNT=1         # Messages per worker
MESSAGE_TTL=3600000             # Message TTL in milliseconds (1 hour)
RETRY_DELAY=60                  # Retry delay in seconds
```

## Usage

### 1. Start the FastAPI Application

The queue system is automatically initialized when the FastAPI app starts:

```bash
uvicorn main:app --host 0.0.0.0 --port 8300
```

### 2. Start the Workers

Workers run as separate processes and must be started independently:

```bash
# Start with default 5 workers
python start_workers.py

# Start with custom number of workers
python start_workers.py --workers 10

# Or use environment variable
NUM_WORKERS=8 python start_workers.py
```

### 3. Execute Jobs

Jobs are now automatically queued when using the execute endpoint:

```bash
curl -X POST "http://localhost:8300/v1/jobs/execute/{job_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response includes queue information:
```json
{
  "message": "Job queued for execution",
  "job_id": "60f7b3b3b3b3b3b3b3b3b3b3",
  "status": "queued",
  "queue_position": 3,
  "estimated_wait_time": "Queue has 2 jobs ahead",
  "active_workers": 5,
  "max_concurrent_jobs": 20
}
```

## API Endpoints

### Queue Status
```bash
GET /v1/jobs/queue/status
```
Returns current queue status including job count, active workers, and capacity.

### Queue Metrics
```bash
GET /v1/jobs/queue/metrics
```
Returns performance metrics including throughput, processing times, and uptime.

### Queue Management
```bash
POST /v1/jobs/queue/purge
```
Purges all jobs from the queue (admin only).

## Job Processing Flow

1. **Job Submission**: Client calls `POST /v1/jobs/execute/{job_id}`
2. **Queue Check**: System checks if queue has capacity
3. **Job Queuing**: Job is added to RabbitMQ queue with metadata
4. **Worker Processing**: Available worker picks up job from queue
5. **Job Execution**: Worker executes job using existing processors
6. **Status Updates**: Job status is updated in database
7. **Webhook Notifications**: Completion webhooks are sent if configured

## Error Handling

### Queue Full
When the queue reaches maximum capacity (50 jobs):
```json
{
  "detail": "Job queue is full. Maximum queue length is 50. Please try again later."
}
```

### Worker Failures
- Jobs are automatically retried up to 3 times
- Failed jobs are marked with error details
- Workers automatically restart on failure

### Connection Issues
- Robust connection handling with automatic reconnection
- Graceful degradation when RabbitMQ is unavailable
- Comprehensive logging for troubleshooting

## Monitoring

### Queue Status
Monitor queue health with:
```bash
curl "http://localhost:8300/v1/jobs/queue/status"
```

### Worker Health
Workers automatically report their status and restart on failure.

### Logs
All components provide detailed logging:
- Queue operations
- Job processing
- Worker status
- Error conditions

## Development

### Running Tests
```bash
pytest app/v1/tests/test_queue_management.py
```

### Local Development
1. Start RabbitMQ: `docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management`
2. Start FastAPI app: `uvicorn main:app --reload`
3. Start workers: `python start_workers.py --workers 2`

## Production Deployment

### Docker Compose
The system is designed to work with the existing docker-compose.yml:

```yaml
services:
  app:
    # FastAPI application
    
  rabbitmq:
    image: rabbitmq:3-management
    # RabbitMQ service
    
  workers:
    # Add worker service
    build: .
    command: python start_workers.py --workers 10
    depends_on:
      - rabbitmq
```

### Scaling
- Scale workers independently: `docker-compose up --scale workers=5`
- Monitor queue metrics to determine optimal worker count
- Adjust `MAX_CONCURRENT_JOBS` based on system resources

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check RabbitMQ is running and accessible
2. **Queue Full**: Increase `MAX_QUEUE_LENGTH` or add more workers
3. **Slow Processing**: Increase worker count or check job processor performance
4. **Memory Issues**: Monitor worker memory usage and restart if needed

### Debug Mode
Enable debug logging:
```bash
python start_workers.py --log-level DEBUG
```

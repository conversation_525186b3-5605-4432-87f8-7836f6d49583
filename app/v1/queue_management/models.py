from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class JobStatus(str, Enum):
    """Job status enumeration"""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class JobQueueMessage(BaseModel):
    """Message format for jobs in the RabbitMQ queue"""
    job_id: str = Field(..., description="Unique job identifier")
    tenant_id: str = Field(..., description="Tenant identifier for database access")
    process_name: str = Field(..., description="Name of the process to execute")
    priority: int = Field(default=0, description="Job priority (higher number = higher priority)")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="When the job was queued")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum number of retry attempts")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional job metadata")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QueueStatus(BaseModel):
    """Current status of the job queue"""
    total_queued: int = Field(..., description="Total number of jobs in queue")
    active_workers: int = Field(..., description="Number of currently active workers")
    max_concurrent_jobs: int = Field(..., description="Maximum allowed concurrent jobs")
    max_queue_length: int = Field(..., description="Maximum allowed queue length")
    queue_full: bool = Field(..., description="Whether the queue is at capacity")
    workers_at_capacity: bool = Field(..., description="Whether all workers are busy")


class QueueConfig(BaseModel):
    """Configuration for the job queue system"""
    max_concurrent_jobs: int = Field(default=20, description="Maximum concurrent job executions")
    max_queue_length: int = Field(default=50, description="Maximum number of jobs in queue")
    rabbitmq_url: str = Field(..., description="RabbitMQ connection URL")
    queue_name: str = Field(default="job_queue", description="Name of the job queue")
    exchange_name: str = Field(default="job_exchange", description="Name of the exchange")
    routing_key: str = Field(default="job.process", description="Routing key for job messages")
    prefetch_count: int = Field(default=1, description="Number of messages to prefetch per worker")
    message_ttl: int = Field(default=3600000, description="Message TTL in milliseconds (1 hour)")
    retry_delay: int = Field(default=60, description="Delay between retries in seconds")


class WorkerStatus(BaseModel):
    """Status of a job worker"""
    worker_id: str = Field(..., description="Unique worker identifier")
    is_active: bool = Field(..., description="Whether the worker is currently processing a job")
    current_job_id: Optional[str] = Field(None, description="ID of currently processing job")
    started_at: datetime = Field(..., description="When the worker started")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    processed_jobs: int = Field(default=0, description="Total number of jobs processed")
    failed_jobs: int = Field(default=0, description="Total number of failed jobs")


class QueueMetrics(BaseModel):
    """Metrics for queue monitoring"""
    total_jobs_processed: int = Field(default=0, description="Total jobs processed since startup")
    total_jobs_failed: int = Field(default=0, description="Total jobs failed since startup")
    average_processing_time: float = Field(default=0.0, description="Average job processing time in seconds")
    queue_throughput: float = Field(default=0.0, description="Jobs processed per minute")
    uptime: float = Field(default=0.0, description="System uptime in seconds")

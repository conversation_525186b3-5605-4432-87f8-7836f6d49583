import asyncio
import json
import uuid
from typing import Optional, Callable, Dict, Any
from datetime import datetime, timezone
import aio_pika
from aio_pika.abc import AbstractIncomingMessage

from .models import JobQueueMessage, WorkerStatus
from .queue_manager import JobQueueManager
from app.core.helper.logger import setup_new_logging
from app.models.user import UserTenantDB
from app.core.database import get_db_from_tenant_id
from app.core.database import AsyncMongoClient
from bson.objectid import ObjectId

# JobStatus imported locally to avoid circular imports

logger = setup_new_logging(__name__)


class JobWorker:
    """
    Async worker that processes jobs from the RabbitMQ queue.
    
    This worker:
    - Consumes jobs from the queue
    - Maintains the existing job processing logic
    - Handles concurrency limits
    - Provides error handling and retries
    """
    
    def __init__(self, queue_manager: JobQueueManager, worker_id: Optional[str] = None):
        self.queue_manager = queue_manager
        self.worker_id = worker_id or str(uuid.uuid4())
        self.status = WorkerStatus(
            worker_id=self.worker_id,
            is_active=False,
            started_at=datetime.now(timezone.utc),
            last_activity=datetime.now(timezone.utc)
        )
        self.is_running = False
        self._stop_event = asyncio.Event()
        
    async def start(self) -> None:
        """Start the worker to consume jobs from the queue"""
        if self.is_running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return
            
        self.is_running = True
        logger.info(f"Starting worker {self.worker_id}")
        
        try:
            await self.queue_manager.connect()
            
            # Start consuming messages
            await self.queue_manager.queue.consume(
                self._process_message,
                no_ack=False  # Manual acknowledgment for reliability
            )
            
            # Wait until stop is requested
            await self._stop_event.wait()
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id} encountered error: {str(e)}")
        finally:
            self.is_running = False
            logger.info(f"Worker {self.worker_id} stopped")
    
    async def stop(self) -> None:
        """Stop the worker gracefully"""
        logger.info(f"Stopping worker {self.worker_id}")
        self._stop_event.set()
        self.is_running = False
    
    async def _process_message(self, message: AbstractIncomingMessage) -> None:
        """Process a single job message from the queue"""
        job_message = None
        
        try:
            # Parse the job message
            message_data = json.loads(message.body.decode())
            job_message = JobQueueMessage(**message_data)
            
            logger.info(f"Worker {self.worker_id} processing job {job_message.job_id}")
            
            # Check concurrency limits
            status = await self.queue_manager.get_queue_status()
            if status.workers_at_capacity:
                logger.warning(f"Workers at capacity, rejecting job {job_message.job_id}")
                await message.reject(requeue=True)
                return
            
            # Mark job as started (counter will be decremented when job completes)
            self.queue_manager.mark_job_started(job_message.job_id)
            self.status.is_active = True
            self.status.current_job_id = job_message.job_id
            self.status.last_activity = datetime.now(timezone.utc)
            
            # Process the job
            success = await self._execute_job(job_message)
            
            if success:
                # Acknowledge successful processing
                await message.ack()
                self.status.processed_jobs += 1
                self.queue_manager.mark_job_completed(job_message.job_id, success=True)
                logger.info(f"Successfully processed job {job_message.job_id}")
            else:
                # Handle retry logic
                await self._handle_job_failure(message, job_message)
                
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            if job_message:
                await self._handle_job_failure(message, job_message, str(e))
            else:
                # If we can't parse the message, reject it
                await message.reject(requeue=False)
        finally:
            # Reset worker status
            self.status.is_active = False
            self.status.current_job_id = None
            self.status.last_activity = datetime.now(timezone.utc)
    
    async def _execute_job(self, job_message: JobQueueMessage) -> bool:
        """
        Execute the actual job using the existing processor logic.
        This maintains the same job execution flow as the original execute_job function.
        """
        # Initialize variables that might be used in error handling
        user_tenant_info = None
        jobs_collection = None

        try:
            # Get tenant database info
            tenant_db = get_db_from_tenant_id(job_message.tenant_id)

            # Create async database connection
            from pymongo import AsyncMongoClient
            import os
            async_client = AsyncMongoClient(os.getenv("MONGO_URI"))
            async_db = async_client[tenant_db.name]

            # Get job from database
            jobs_collection = async_db.jobs
            job = await jobs_collection.find_one({"_id": ObjectId(job_message.job_id)})

            if not job:
                logger.error(f"Job {job_message.job_id} not found in database")
                return False

            # Create dummy user and role for worker (workers don't need real user info)
            from app.models.role import Role
            from app.models.user import User

            dummy_role = Role(
                _id="worker_role",
                name="worker"
            )

            dummy_user = User(
                _id="worker_user",
                username="queue_worker",
                role=dummy_role
            )

            # Create UserTenantDB object for worker
            user_tenant_info = UserTenantDB(
                tenant_id=job_message.tenant_id,
                db=tenant_db,
                user=dummy_user,
                async_db=async_db
            )
            
            # Import JobStatus locally to avoid circular imports
            from app.v1.api.jobs.models import JobStatus

            # Update job status to processing
            await jobs_collection.update_one(
                {"_id": ObjectId(job_message.job_id)},
                {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.now(timezone.utc)}}
            )
            
            # Import processors locally to avoid circular imports
            from app.v1.processes.extract_data_from_image import ExtractQuestions
            from app.v1.processes.audio_analysis import AudioAnalysis
            from app.v1.processes.generic_entity_extraction import GenericEntity
            from app.v1.processes.voice_cloning import VoiceCloning

            # Select the appropriate processor based on process_name
            processor = None
            if job_message.process_name == "extract-image-data":
                processor = ExtractQuestions(user_tenant_info)
            elif job_message.process_name == "audio-transcribe-analysis":
                processor = AudioAnalysis(user_tenant_info)
            elif job_message.process_name == "generic-entity-extraction":
                processor = GenericEntity(user_tenant_info)
            elif job_message.process_name == "voice-cloning":
                processor = VoiceCloning(user_tenant_info)
            else:
                # Default to ExtractQuestions for backward compatibility
                processor = ExtractQuestions(user_tenant_info)
            
            # Execute the job processing
            await processor.extract_from_job(job_message.job_id)
            
            # Handle webhook notifications if needed
            await self._handle_webhook_notification(job_message.job_id, JobStatus.COMPLETED, user_tenant_info)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing job {job_message.job_id}: {str(e)}")

            # Update job status to failed (only if we have database connection)
            try:
                if jobs_collection is not None:
                    await jobs_collection.update_one(
                        {"_id": ObjectId(job_message.job_id)},
                        {"$set": {"status": JobStatus.FAILED, "error": str(e)}}
                    )

                # Send failure webhook notification (only if we have user_tenant_info)
                if user_tenant_info is not None:
                    await self._handle_webhook_notification(job_message.job_id, JobStatus.FAILED, user_tenant_info, str(e))

            except Exception as update_error:
                logger.error(f"Failed to update job status: {str(update_error)}")

            return False
    
    async def _handle_job_failure(self, message: AbstractIncomingMessage, job_message: JobQueueMessage, error: str = None) -> None:
        """Handle job failure with retry logic"""
        try:
            if job_message.retry_count < job_message.max_retries:
                # Increment retry count and requeue
                job_message.retry_count += 1
                logger.info(f"Retrying job {job_message.job_id} (attempt {job_message.retry_count}/{job_message.max_retries})")
                
                # Requeue the job with updated retry count
                await self.queue_manager.enqueue_job(job_message)
                await message.ack()  # Acknowledge the original message
            else:
                # Max retries exceeded, mark as failed
                logger.error(f"Job {job_message.job_id} failed after {job_message.max_retries} retries")
                await message.reject(requeue=False)
                self.status.failed_jobs += 1
            
            self.queue_manager.mark_job_completed(job_message.job_id, success=False)
            
        except Exception as e:
            logger.error(f"Error handling job failure: {str(e)}")
            await message.reject(requeue=False)
    
    async def _handle_webhook_notification(self, job_id: str, status: str, user_tenant_info: UserTenantDB, error: str = None) -> None:
        """Handle webhook notifications for job completion"""
        try:
            # Check if project has webhook_callback URL
            projects_collection = user_tenant_info.async_db.projects
            jobs_collection = user_tenant_info.async_db.jobs
            
            job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
            if not job:
                return
                
            project = await projects_collection.find_one({"_id": job["project_id"]})
            if project and project.get("webhook_callback"):
                # Import and use the existing webhook notification function
                from app.v1.api.jobs import send_webhook_notification
                from fastapi import BackgroundTasks
                
                # Create a background tasks object for webhook
                background_tasks = BackgroundTasks()
                await send_webhook_notification(job_id, status, user_tenant_info, background_tasks, error)
                
                # Execute webhook tasks
                for task in background_tasks.tasks:
                    await task()
                    
        except Exception as e:
            logger.error(f"Error sending webhook notification: {str(e)}")
    
    def get_status(self) -> WorkerStatus:
        """Get current worker status"""
        return self.status

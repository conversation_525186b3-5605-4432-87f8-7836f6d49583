import pytest
import async<PERSON>
import os
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from app.v1.queue_management.models import JobQueueMessage, QueueConfig, QueueStatus
from app.v1.queue_management.queue_manager import JobQueueManager
from app.v1.queue_management.worker import <PERSON><PERSON><PERSON><PERSON>
from app.v1.queue_management.config import get_queue_config


class TestQueueConfig:
    """Test queue configuration"""
    
    def test_default_config(self):
        """Test default configuration values"""
        with patch.dict(os.environ, {
            "RABBITMQ_DEFAULT_USER": "test_user",
            "RABBITMQ_DEFAULT_PASS": "test_pass",
            "RABBITMQ_HOST": "localhost"
        }):
            config = get_queue_config()
            
            assert config.max_concurrent_jobs == 20
            assert config.max_queue_length == 50
            assert config.queue_name == "job_queue"
            assert "test_user:test_pass@localhost" in config.rabbitmq_url
    
    def test_custom_config(self):
        """Test custom configuration from environment"""
        with patch.dict(os.environ, {
            "MAX_CONCURRENT_JOBS": "30",
            "MAX_QUEUE_LENGTH": "100",
            "RABBITMQ_URL": "amqp://custom:<EMAIL>:5672/"
        }):
            config = get_queue_config()
            
            assert config.max_concurrent_jobs == 30
            assert config.max_queue_length == 100
            assert config.rabbitmq_url == "amqp://custom:<EMAIL>:5672/"


class TestJobQueueMessage:
    """Test job queue message model"""
    
    def test_job_message_creation(self):
        """Test creating a job queue message"""
        message = JobQueueMessage(
            job_id="test_job_123",
            tenant_id="tenant_456",
            process_name="extract-image-data"
        )
        
        assert message.job_id == "test_job_123"
        assert message.tenant_id == "tenant_456"
        assert message.process_name == "extract-image-data"
        assert message.priority == 0
        assert message.retry_count == 0
        assert message.max_retries == 3
        assert isinstance(message.created_at, datetime)
    
    def test_job_message_serialization(self):
        """Test job message JSON serialization"""
        message = JobQueueMessage(
            job_id="test_job_123",
            tenant_id="tenant_456",
            process_name="extract-image-data",
            metadata={"project_id": "project_789"}
        )
        
        json_data = message.model_dump_json()
        assert "test_job_123" in json_data
        assert "tenant_456" in json_data
        assert "extract-image-data" in json_data
        assert "project_789" in json_data


class TestJobQueueManager:
    """Test job queue manager"""
    
    @pytest.fixture
    def mock_config(self):
        """Mock queue configuration"""
        return QueueConfig(
            max_concurrent_jobs=2,
            max_queue_length=5,
            rabbitmq_url="amqp://test:test@localhost:5672/",
            queue_name="test_queue"
        )
    
    @pytest.fixture
    def queue_manager(self, mock_config):
        """Create queue manager with mock config"""
        return JobQueueManager(mock_config)
    
    @pytest.mark.asyncio
    async def test_queue_manager_initialization(self, queue_manager):
        """Test queue manager initialization"""
        assert queue_manager.config.max_concurrent_jobs == 2
        assert queue_manager.config.max_queue_length == 5
        assert len(queue_manager.active_jobs) == 0
        assert queue_manager.connection is None
    
    @pytest.mark.asyncio
    async def test_job_tracking(self, queue_manager):
        """Test job tracking for concurrency control"""
        # Mark jobs as started
        queue_manager.mark_job_started("job1")
        queue_manager.mark_job_started("job2")
        
        assert len(queue_manager.active_jobs) == 2
        assert "job1" in queue_manager.active_jobs
        assert "job2" in queue_manager.active_jobs
        
        # Mark job as completed
        queue_manager.mark_job_completed("job1", success=True)
        
        assert len(queue_manager.active_jobs) == 1
        assert "job1" not in queue_manager.active_jobs
        assert "job2" in queue_manager.active_jobs
        assert queue_manager.metrics.total_jobs_processed == 1
    
    @pytest.mark.asyncio
    async def test_queue_status_without_connection(self, queue_manager):
        """Test getting queue status without RabbitMQ connection"""
        # Mock the queue to avoid actual connection
        with patch.object(queue_manager, 'connect', new_callable=AsyncMock):
            with patch.object(queue_manager, 'queue') as mock_queue:
                mock_queue.get_info.return_value = MagicMock(message_count=3)
                
                status = await queue_manager.get_queue_status()
                
                assert isinstance(status, QueueStatus)
                assert status.total_queued == 3
                assert status.max_concurrent_jobs == 2
                assert status.max_queue_length == 5


class TestJobWorker:
    """Test job worker"""
    
    @pytest.fixture
    def mock_queue_manager(self):
        """Mock queue manager"""
        manager = MagicMock()
        manager.connect = AsyncMock()
        manager.queue = MagicMock()
        manager.get_queue_status = AsyncMock(return_value=QueueStatus(
            total_queued=0,
            active_workers=0,
            max_concurrent_jobs=20,
            max_queue_length=50,
            queue_full=False,
            workers_at_capacity=False
        ))
        return manager
    
    @pytest.fixture
    def worker(self, mock_queue_manager):
        """Create worker with mock queue manager"""
        return JobWorker(mock_queue_manager, worker_id="test-worker")
    
    def test_worker_initialization(self, worker):
        """Test worker initialization"""
        assert worker.worker_id == "test-worker"
        assert not worker.is_running
        assert not worker.status.is_active
        assert worker.status.processed_jobs == 0
    
    @pytest.mark.asyncio
    async def test_worker_status_tracking(self, worker):
        """Test worker status tracking"""
        # Initially not active
        assert not worker.status.is_active
        assert worker.status.current_job_id is None
        
        # Simulate job processing
        worker.status.is_active = True
        worker.status.current_job_id = "test_job_123"
        worker.status.processed_jobs += 1
        
        status = worker.get_status()
        assert status.is_active
        assert status.current_job_id == "test_job_123"
        assert status.processed_jobs == 1


class TestIntegration:
    """Integration tests for the queue system"""
    
    @pytest.mark.asyncio
    async def test_job_message_flow(self):
        """Test complete job message flow"""
        # Create job message
        message = JobQueueMessage(
            job_id="integration_test_job",
            tenant_id="test_tenant",
            process_name="extract-image-data",
            metadata={"test": True}
        )
        
        # Verify message properties
        assert message.job_id == "integration_test_job"
        assert message.process_name == "extract-image-data"
        assert message.metadata["test"] is True
        
        # Test serialization/deserialization
        json_str = message.model_dump_json()
        parsed_data = JobQueueMessage.model_validate_json(json_str)
        
        assert parsed_data.job_id == message.job_id
        assert parsed_data.tenant_id == message.tenant_id
        assert parsed_data.process_name == message.process_name
        assert parsed_data.metadata == message.metadata
    
    @pytest.mark.asyncio
    async def test_concurrency_limits(self):
        """Test concurrency limit enforcement"""
        config = QueueConfig(
            max_concurrent_jobs=2,
            max_queue_length=5,
            rabbitmq_url="amqp://test:test@localhost:5672/"
        )
        
        manager = JobQueueManager(config)
        
        # Start jobs up to limit
        manager.mark_job_started("job1")
        manager.mark_job_started("job2")
        
        # Check status
        with patch.object(manager, 'connect', new_callable=AsyncMock):
            with patch.object(manager, 'queue') as mock_queue:
                mock_queue.get_info.return_value = MagicMock(message_count=0)
                
                status = await manager.get_queue_status()
                
                assert status.active_workers == 2
                assert status.workers_at_capacity is True
        
        # Complete one job
        manager.mark_job_completed("job1", success=True)
        
        with patch.object(manager, 'connect', new_callable=AsyncMock):
            with patch.object(manager, 'queue') as mock_queue:
                mock_queue.get_info.return_value = MagicMock(message_count=0)
                
                status = await manager.get_queue_status()
                
                assert status.active_workers == 1
                assert status.workers_at_capacity is False


# Pytest configuration for async tests
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

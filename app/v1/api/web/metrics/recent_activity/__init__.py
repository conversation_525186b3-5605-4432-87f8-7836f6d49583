from fastapi import APIRouter, Depends, Query, HTTPException

from app.core.security import get_tenant_info, UserTenantDB
from app.core.helper.mongo_helper import serialize_mongo_doc
from app.v1.schema.pagination import PaginationResponse

from .models import JobActivity

activity_router = APIRouter()

@activity_router.get("/recent-activity")
async def get_recent_job_activities(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    status: str = Query(None),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info),
) -> PaginationResponse[JobActivity]:

    try:

        skip = (page - 1) * limit
        match_stage = {"status": status} if status else {}

        pipeline = []
        if match_stage:
            pipeline.append({"$match": match_stage})

        pipeline += [
            {"$sort": {"created_at": -1}},
            {"$skip": skip},
            {"$limit": limit},

            # Lookup username
            {
                    "$lookup": {
                        "from": "users",
                        "localField": "created_by",
                        "foreignField": "_id",
                        "as": "creator"
                    }
                },
                {
                    "$unwind": {
                        "path": "$creator",
                        "preserveNullAndEmptyArrays": True  # Prevents crash if user not found
                    }
                },
                {
                    "$addFields": {
                        "created_by_name": { "$ifNull": ["$creator.username", "Unknown"] }
                    }
            },

            {
                "$project": {
                    "_id": 1,
                    "status": 1,
                    "created_at": 1,
                    "created_by":1,
                    "created_by_name": 1,
                    "completed_at":1,
                    "process_name": 1,
                    "job_name": "$name",
                    "event": {
                        "$concat": [
                            "Job ",
                            {
                                "$cond": [
                                    {"$eq": ["$status", "completed"]},
                                    "completed",
                                    {
                                        "$cond": [
                                            {"$eq": ["$status", "failed"]},
                                            "failed",
                                            {
                                                "$cond": [
                                                    {"$eq": ["$status", "partially-completed"]},
                                                    "partially completed",
                                                    "created"
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                }
            }
        ]

        collection = user_tenant_info.db["jobs"]
        total_jobs = collection.count_documents(match_stage)
        results = collection.aggregate(pipeline).to_list(length=None)

        # Convert _id to id
        for item in results:
            item["id"] = str(item.pop("_id"))

        return {
        "data": serialize_mongo_doc(results),
        "meta": {
            "page": page,
            "limit": limit,
            "total": total_jobs,
            "total_pages": (total_jobs + limit - 1) // limit,
        },
            }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))



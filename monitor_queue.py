#!/usr/bin/env python3
"""
Real-time queue monitoring script.
Run this in a separate terminal while running the load test.
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime

# Configuration
QUEUE_STATUS_URL = "http://127.0.0.1:8000/v1/jobs/queue/status"
QUEUE_METRICS_URL = "http://127.0.0.1:8000/v1/jobs/queue/metrics"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyYzJiNGQwZWIwOTkxN2U1ZDkzOTU0IiwiZXhwIjoxNzU0OTAyMTY2fQ.V7eDu5craCmJlwVhQIbnFYtswrfglf2FybEKm2e9V8w"

async def get_queue_status():
    """Get current queue status"""
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {TOKEN}'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(QUEUE_STATUS_URL, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Status code: {response.status}"}
    except Exception as e:
        return {"error": str(e)}

async def get_queue_metrics():
    """Get queue metrics"""
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {TOKEN}'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(QUEUE_METRICS_URL, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Status code: {response.status}"}
    except Exception as e:
        return {"error": str(e)}

def format_status_line(status_data, metrics_data):
    """Format a single status line"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    if "error" in status_data:
        return f"[{timestamp}] ❌ Error: {status_data['error']}"
    
    queue_status = status_data.get("queue_status", {})
    metrics = metrics_data.get("metrics", {}) if "error" not in metrics_data else {}
    
    queued = queue_status.get("total_queued", "?")
    active = queue_status.get("active_workers", "?")
    max_concurrent = queue_status.get("max_concurrent_jobs", "?")
    max_queue = queue_status.get("max_queue_length", "?")
    queue_full = "🔴" if queue_status.get("queue_full", False) else "🟢"
    workers_full = "🔴" if queue_status.get("workers_at_capacity", False) else "🟢"
    
    processed = metrics.get("total_jobs_processed", "?")
    failed = metrics.get("total_jobs_failed", "?")
    avg_time = metrics.get("average_processing_time", 0)
    throughput = metrics.get("queue_throughput", 0)
    
    return (f"[{timestamp}] Queue: {queued}/{max_queue} {queue_full} | "
            f"Workers: {active}/{max_concurrent} {workers_full} | "
            f"Processed: {processed} | Failed: {failed} | "
            f"Avg: {avg_time:.1f}s | Throughput: {throughput:.1f}/min")

async def monitor_queue():
    """Monitor queue status in real-time"""
    print("🔍 Real-time Queue Monitor")
    print("=" * 80)
    print("Legend: 🟢 = OK, 🔴 = At Capacity")
    print("Press Ctrl+C to stop")
    print("=" * 80)
    
    try:
        while True:
            # Get both status and metrics
            status_task = get_queue_status()
            metrics_task = get_queue_metrics()
            
            status_data, metrics_data = await asyncio.gather(status_task, metrics_task)
            
            # Format and print status line
            status_line = format_status_line(status_data, metrics_data)
            print(status_line)
            
            # Wait before next update
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Monitoring stopped")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")

def main():
    """Main function"""
    asyncio.run(monitor_queue())

if __name__ == "__main__":
    main()
